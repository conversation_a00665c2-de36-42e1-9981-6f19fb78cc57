<template>
  <div class="flex flex-row gap-2 flex-wrap mt-2 mb-2">
    <UBadge
      color="primary"
      variant="subtle"
      size="sm"
    >
      {{ $t(data.type) }}
    </UBadge>
    <UBadge
      color="neutral"
      variant="subtle"
      size="sm"
    >
      {{ $t(data.model_name) }}
    </UBadge>
    <UBadge
      color="warning"
      variant="subtle"
      size="sm"
    >
      {{ $t(data.used_credit) }} {{ $t("Credits") }}
    </UBadge>
  </div>
  <div class="mt-2 grid grid-cols-1 sm:grid-cols-2 gap-4">
    <UButton
      icon="bxs:detail"
      color="neutral"
      variant="ghost"
      size="sm"
      class="text-white hover:text-gray-800 bg-white/10 w-full"
      :label="$t('Detail')"
      @click.stop="openFullScreen"
    />
    <UButton
      icon="material-symbols:delete"
      color="neutral"
      variant="ghost"
      size="sm"
      class="text-white hover:text-gray-800 bg-white/10 w-full"
      :label="$t('Delete')"
      @click.stop="handleDelete"
    />
    <BaseDownloadButton
      :link="firstImage?.image_url"
      :label="$t('Download')"
      size="sm"
      block
      class="text-white hover:text-gray-800 bg-white/10 w-full col-span-2"
      variant="ghost"
    />
    <UButton
      v-if="showCloseButton"
      icon="material-symbols:close"
      color="neutral"
      variant="soft"
      size="sm"
      class="mt-auto text-white hover:text-gray-800 bg-transparent w-full col-span-2 justify-center"
      :label="$t('Close')"
      @click.stop="() => emits('close')"
    />
  </div>
</template>

<script setup lang="ts">
const { t } = useI18n()
const historyStore = useHistoryStore()
const { openConfirm } = useConfirm()
const toast = useToast()
const router = useRouter()
const { historyDetail, showDetailModal } = storeToRefs(historyStore)
const props = defineProps({
  data: {
    type: Object,
    default: () => ({})
  },
  showCloseButton: {
    type: Boolean,
    default: false
  }
})

const emits = defineEmits(['close'])

const firstImage = computed(() => {
  return props.data?.generated_image?.[0] || {}
})

// Handle delete action
const handleDelete = () => {
  openConfirm({
    title: t('confirmDelete') || 'Confirm Delete',
    description:
      t('confirmDeleteDescription')
      || 'Are you sure you want to delete this item? This action cannot be undone.',
    icon: 'i-lucide-trash-2',
    confirmText: t('delete') || 'Delete',
    cancelText: t('cancel') || 'Cancel',
    onConfirm: async () => {
      const result = await historyStore.deleteHistory(props.data.id)
      if (result !== null) {
        toast.add({
          title: t('success.deleted') || 'Success',
          description:
            t('historyDeleted') || 'History item deleted successfully',
          color: 'success'
        })
      } else {
        toast.add({
          title: t('error.general') || 'Error',
          description:
            historyStore.errors.deleteHistory?.message
            || t('deleteError')
            || 'Failed to delete history item',
          color: 'error'
        })
        // Throw error to prevent modal from closing
        throw new Error('Delete failed')
      }
    }
  })
}

const openFullScreen = () => {
  historyDetail.value = props.data as any
  showDetailModal.value = true
  // Update the URL to include the ID for navigation
  if (props.data.uuid) {
    router.push({ query: { uuid: props.data.uuid } })
  }
}
</script>
